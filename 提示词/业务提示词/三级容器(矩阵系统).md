# 三级容器(矩阵系统)

## 基于‘compnetA1.tsx’容器布局

**1.组件布局(logicA1.tsx):**

- 1.装载容器:'componetA1'
- 2.存放组件:'featureA1'
- 3.排列方式:网格排列
  - 1.网格行数:33
  - 2.网格列数:33
- 4.组件间隔:'margin: 1vW'
- 5.组件坐标:
  - 1.计算以中心组件为原点的所有组件坐标
  - 2.坐标说明:
    - 1.原点坐标:组件坐标为(0,0)

## 功能组件定义

**1.功能组件1(featureA1.tsx):**

- 1.组件形状: 矩形
- 2.组件高度:('componetA1'容器高度 - 网格行数 + 1 * 组件间隔) / 网格行数
- 3.组件宽度:('componetA1'容器宽度 - 网格列数 + 1 * 组件间隔) / 网格列数
- 4.背景颜色:#cecece
- 5.组件圆角:5px
- 6.展示方式:弹性布局
- 7.弹性方向:垂直
- 8.对齐方式:水平，垂直居中
- 9.溢出处理:隐藏
- 10.组件定位:相对定位
- 11.组件坐标:通过计算得到的坐标
- 12.鼠标指针:手型

**2.文字样式:**

- 1.默认文本:无
- 2.字体大小:随组件大小变化
- 3.字体颜色:#242424
- 4.字体对齐:居中
- 5.字体行高:1.5

## 组件交互逻辑

**1.组件点击(logicA1.tsx):**

- 1.持续状态:
  - 1.状态:'mode:toggle'
  - 2.激活:点击后切换并保持‘激活’/‘未激活’状态
  - 3.样式:
    - 1.激活状态(isActive: true):
      - 1.背景颜色:#929292
      - 2.组件边框:3px
      - 3.边框颜色:#ffd500
    - 2.未激活状态(isActive: false):
      - 1.背景颜色:#cecece

**2.状态初始化(logicA1.tsx):**

- 1.组件状态初始化:
  - 1.访问状态:访问‘store.ts’状态管理器
    - 1.获取状态:获取‘初始化按键’状态(true/false)
    - 2.按键状态:状态为‘true’启用初始化处理
  - 3.初始处理:
    - 1.组件:'featureA1'组件状态全部设置为未激活

**3.坐标显示(logicA1.tsx):**

- 1.访问状态:访问‘store.ts’状态管理器
- 2.获取状态:获取‘坐标按键’状态
  - 1.按键状态:
    - 1.状态为‘true’启用坐标显示
    - 2.状态为‘false’关闭坐标显示

**4.鼠标悬停(logicA1.tsx):**

- 1.样式:
  - 1.组件: 悬浮前置
  - 2.组件大小:放大1.2倍
