# 一二级容器路径

## 创建页面路由文件

**页面路由文件:**

- 1.创建文件‘layout.tsx’ -> ('根目录/frontend/app')
- 2.创建文件‘page.tsx’ -> ('根目录/frontend/app')

## 创建一级容器文件

**一级容器文件:**

- 1.创建文件‘main_container.tsx’ -> ('根目录/frontend/componets/componet‘)

## 创建容器交互文件

**容器交互文件:**

- 1，创建文件‘componet_interactionB.tsx’ -> ('根目录/frontend/componets/interaction')

## 创建二级容器文件

**二级容器文件:**

- 1.创建文件‘componetA1.tsx’ -> (''根目录/frontend/componets/componet')
- 2.创建文件‘componetB1.tsx’ -> (''根目录/frontend/componets/componet')
- 3.创建文件‘componetB2.tsx’ -> (''根目录/frontend/componets/componet')
- 4.创建文件‘componetButton.tsx’ -> (''根目录/frontend/componets/componet')
